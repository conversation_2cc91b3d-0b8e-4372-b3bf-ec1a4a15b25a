import { logging } from "@skywind-group/sw-utils";
import { getTapkingAdapter } from "./tapkingAdapter";
import { DomainSources, DomainWatcherAdapter, DomainStatus, DomainSource } from "./types";
import { DomainWatcherAdapterNotSupportedError } from "../../errors";
import { getDynamicDomainService, getStaticDomainService } from "../domain";
import { getStaticDomainPoolService } from "../staticDomainPool";
import { getDynamicDomainPoolService } from "../dynamicDomainPool";
import { Notificator } from "./notificationService";
import config from "../../config";

interface DomainWatcherInfo {
    domainId?: number;
    status?: DomainStatus;
    sources?: DomainSource[];
    dirty?: boolean;
}

export class DomainWatcher {
    private readonly domains: Map<string, DomainWatcherInfo> = new Map();

    constructor(private readonly adapter: DomainWatcherAdapter, private readonly notificator: Notificator) {
    }

    public async update(domains: DomainSources, log?: logging.Logger) {
        const list = await this.adapter.list();
        for (const { domain, status } of list) {
            const existing = this.domains.get(domain);
            this.domains.set(domain, {
                domainId: existing?.domainId,
                status,
                sources: existing?.sources,
                dirty: true
            });
        }
        for (const domain of this.domains.keys()) {
            if (!domains.has(domain)) {
                await this.adapter.remove(domain);
                this.domains.delete(domain);
            }
        }
        for (const [domain, { domainId, sources }] of domains.entries()) {
            if (this.domains.has(domain)) {
                const existing = this.domains.get(domain);
                this.domains.set(domain, {
                    domainId,
                    status: existing?.status,
                    sources,
                    dirty: existing?.dirty ?? false
                });
            } else {
                await this.adapter.register(domain);
                this.domains.set(domain, {
                    domainId,
                    status: { accessStatus: "UNKNOWN", lastCheckedAt: new Date() },
                    sources,
                    dirty: true
                });
            }
        }
        for (const [domain, { domainId, status, sources, dirty }] of this.domains.entries()) {
            log?.info({ domain, domainId, status, sources, dirty }, "Set status %s for domain %s", status?.accessStatus, domain);
            if (dirty && domainId && sources?.length > 0) {
                for (const { type, staticType, domainId, poolId, environment } of sources) {
                    if (staticType) {
                        log?.info({ domainId, status }, "Update staticDomain");
                        await getStaticDomainService().update({ id: domainId, info: { monitoringStatus: { [this.adapter.adapterId]: status } } });
                    } else {
                        log?.info({ domainId, status }, "Update dynamicDomain");
                        await getDynamicDomainService().update({ id: domainId, info: { monitoringStatus: { [this.adapter.adapterId]: status } } });
                    }

                    if (status.accessStatus === "BLOCKED") {
                        if (type === "staticPool") {
                            const service = getStaticDomainPoolService();
                            log?.info({ poolId, domainId, status }, "Update staticPool");
                            await service.setBlocked(poolId, domainId, status.lastCheckedAt);

                            const count = await service.countDomains(poolId, { type: staticType });
                            if (count < config.slackNotifications.domainPoolAlertThreshold) {
                                // Send Slack alert for blocked domain in static pool
                                await this.notificator.sendStaticPoolDomainBlockedAlert(domain, status.lastCheckedAt, poolId);
                            }
                        }
                        if (type === "dynamicPool") {
                            const service = getDynamicDomainPoolService();
                            log?.info({ poolId, domainId, status }, "Update dynamicPool");
                            await service.setBlocked(poolId, domainId, status.lastCheckedAt);

                            const count = await service.countDomains(poolId, { environment });
                            if (count < config.slackNotifications.domainPoolAlertThreshold) {
                                // Send Slack alert for blocked domain in dynamic pool
                                await this.notificator.sendDynamicPoolDomainBlockedAlert(domain, status.lastCheckedAt, poolId);
                            }
                        }
                        if (type === "entity") {
                            // Send Slack alert for any blocked domain (including standalone domains)
                            await this.notificator.sendDefaultDomainBlockedAlert(domainId, domain, status.lastCheckedAt);
                        }
                    }
                }
                if (status?.accessStatus === "BLOCKED") {
                    await this.adapter.remove(domain);
                    this.domains.delete(domain);
                }
            }
        }
    }



}

function getAdapter(adapter: string, log?: logging.Logger): DomainWatcherAdapter {
    if (adapter === "tapking") {
        return getTapkingAdapter(log);
    }
    throw new DomainWatcherAdapterNotSupportedError(adapter);
}

export function getDomainWatcher(adapter: string, log?: logging.Logger): DomainWatcher {
    return new DomainWatcher(getAdapter(adapter, log), new Notificator());
}
