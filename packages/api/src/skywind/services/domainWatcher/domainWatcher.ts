import { logging } from "@skywind-group/sw-utils";
import { getTapkingAdapter } from "./tapkingAdapter";
import { DomainSources, DomainWatcherAdapter, DomainStatus, DomainSource } from "./types";
import { DomainWatcherAdapterNotSupportedError } from "../../errors";
import { getDynamicDomainService, getStaticDomainService } from "../domain";
import { getStaticDomainPoolService } from "../staticDomainPool";
import { getDynamicDomainPoolService } from "../dynamicDomainPool";
import { Models } from "../../models/models";
import { Op } from "sequelize";
import { EntityStatus } from "../../entities/entity";
import { getSlackNotificationService } from "../slackNotification";
import { BlockedDomainAlert, DefaultDomainBlockedAlert, SlackMessageFormatter } from "./slackMessageFormatter";
import config from "../../config";

interface DomainWatcherInfo {
    domainId?: number;
    status?: DomainStatus;
    sources?: DomainSource[];
    dirty?: boolean;
}

export class DomainWatcher {
    private readonly domains: Map<string, DomainWatcherInfo> = new Map();
    private readonly slackService = getSlackNotificationService();
    private readonly messageFormatter = new SlackMessageFormatter();

    constructor(private readonly adapter: DomainWatcherAdapter) {
    }

    public async update(domains: DomainSources, log?: logging.Logger) {
        const list = await this.adapter.list();
        for (const { domain, status } of list) {
            const existing = this.domains.get(domain);
            this.domains.set(domain, {
                domainId: existing?.domainId,
                status,
                sources: existing?.sources,
                dirty: true
            });
        }
        for (const domain of this.domains.keys()) {
            if (!domains.has(domain)) {
                await this.adapter.remove(domain);
                this.domains.delete(domain);
            }
        }
        for (const [domain, { domainId, sources }] of domains.entries()) {
            if (this.domains.has(domain)) {
                const existing = this.domains.get(domain);
                this.domains.set(domain, {
                    domainId,
                    status: existing?.status,
                    sources,
                    dirty: existing?.dirty ?? false
                });
            } else {
                await this.adapter.register(domain);
                this.domains.set(domain, {
                    domainId,
                    status: { accessStatus: "UNKNOWN", lastCheckedAt: new Date() },
                    sources,
                    dirty: true
                });
            }
        }
        for (const [domain, { domainId, status, sources, dirty }] of this.domains.entries()) {
            log?.info({ domain, domainId, status, sources, dirty }, "Set status %s for domain %s", status?.accessStatus, domain);
            if (dirty && domainId && sources?.length > 0) {
                for (const { type, staticType, domainId, poolId, environment } of sources) {
                    if (staticType) {
                        log?.info({ domainId, status }, "Update staticDomain");
                        await getStaticDomainService().update({ id: domainId, info: { monitoringStatus: { [this.adapter.adapterId]: status } } });
                    } else {
                        log?.info({ domainId, status }, "Update dynamicDomain");
                        await getDynamicDomainService().update({ id: domainId, info: { monitoringStatus: { [this.adapter.adapterId]: status } } });
                    }

                    if (status.accessStatus === "BLOCKED") {
                        if (type === "staticPool") {
                            const service = getStaticDomainPoolService();
                            log?.info({ poolId, domainId, status }, "Update staticPool");
                            await service.setBlocked(poolId, domainId, status.lastCheckedAt);

                            const count = await service.countDomains(poolId, { type: staticType });
                            if (count < config.slackNotifications.domainPoolAlertThreshold) {
                                // Send Slack alert for blocked domain in static pool
                                await this.sendStaticPoolDomainBlockedAlert(domain, status.lastCheckedAt, poolId);
                            }
                        }
                        if (type === "dynamicPool") {
                            const service = getDynamicDomainPoolService();
                            log?.info({ poolId, domainId, status }, "Update dynamicPool");
                            await service.setBlocked(poolId, domainId, status.lastCheckedAt);

                            const count = await service.countDomains(poolId, { environment });
                            if (count < config.slackNotifications.domainPoolAlertThreshold) {
                                // Send Slack alert for blocked domain in dynamic pool
                                await this.sendDynamicPoolDomainBlockedAlert(domain, status.lastCheckedAt, poolId);
                            }
                        }
                        if (type === "entity") {
                            // Send Slack alert for any blocked domain (including standalone domains)
                            await this.sendDefaultDomainBlockedAlert(domainId, domain, status.lastCheckedAt);
                        }
                    }
                }
                if (status?.accessStatus === "BLOCKED") {
                    await this.adapter.remove(domain);
                    this.domains.delete(domain);
                }
            }
        }
    }

    private async sendStaticPoolDomainBlockedAlert(
        domain: string,
        blockedAt: Date,
        poolId: number
    ): Promise<void> {
        try {
            // Check static domain pool
            const staticPool = await Models.StaticDomainPoolModel.findByPk(poolId);
            if (staticPool) {
                const alert: BlockedDomainAlert = {
                    domain,
                    blockedAt,
                    poolId,
                    reason: "Domain blocked by monitoring adapter"
                };

                const message = this.messageFormatter.formatBlockedDomainMessage(alert);
                await this.slackService.sendFormattedMessage(message);
            }
        } catch (error) {
            // Log error but don't throw to avoid breaking domain monitoring
            console.error(`Failed to send static pool domain blocked alert: ${error.message}`);
        }
    }

    private async sendDynamicPoolDomainBlockedAlert(
        domain: string,
        blockedAt: Date,
        poolId: number
    ): Promise<void> {
        try {
            // Check dynamic domain pool
            const dynamicPool = await Models.DynamicDomainPoolModel.findByPk(poolId);
            if (dynamicPool) {
                const alert: BlockedDomainAlert = {
                    domain,
                    blockedAt,
                    poolId,
                    reason: "Domain blocked by monitoring adapter"
                };

                const message = this.messageFormatter.formatBlockedDomainMessage(alert);
                await this.slackService.sendFormattedMessage(message);
            }
        } catch (error) {
            // Log error but don't throw to avoid breaking domain monitoring
            console.error(`Failed to send dynamic pool domain blocked alert: ${error.message}`);
        }
    }

    private async sendDefaultDomainBlockedAlert(domainId: number, domain: string, blockedAt: Date): Promise<void> {
        try {
            // Check if this domain is used as a default domain by any entity
            const entitiesUsingDomain = await Models.EntityModel.findAll({
                where: {
                    [Op.or]: [
                        { staticDomainId: domainId },
                        { dynamicDomainId: domainId },
                        { lobbyDomainId: domainId },
                        { liveStreamingDomainId: domainId },
                        { ehubDomainId: domainId }
                    ],
                    status: EntityStatus.NORMAL
                }
            });

            if (entitiesUsingDomain.length > 0) {
                // Determine domain type from the first entity using this domain
                const entity = entitiesUsingDomain[0];
                let domainType: string;

                if (entity.staticDomainId === domainId) {
                    domainType = "static";
                } else if (entity.dynamicDomainId === domainId) {
                    domainType = "dynamic";
                } else if (entity.lobbyDomainId === domainId) {
                    domainType = "lobby";
                } else if (entity.liveStreamingDomainId === domainId) {
                    domainType = "liveStreaming";
                } else if (entity.ehubDomainId === domainId) {
                    domainType = "ehub";
                } else {
                    return;
                }

                const alert: DefaultDomainBlockedAlert = {
                    domain,
                    blockedAt,
                    domainType: domainType as any
                };

                const message = this.messageFormatter.formatDefaultDomainBlockedMessage(alert);
                await this.slackService.sendFormattedMessage(message);
            }
        } catch (error) {
            // Log error but don't throw to avoid breaking domain monitoring
            console.error(`Failed to send default domain blocked alert: ${error.message}`);
        }
    }

}

function getAdapter(adapter: string, log?: logging.Logger): DomainWatcherAdapter {
    if (adapter === "tapking") {
        return getTapkingAdapter(log);
    }
    throw new DomainWatcherAdapterNotSupportedError(adapter);
}

export function getDomainWatcher(adapter: string, log?: logging.Logger): DomainWatcher {
    return new DomainWatcher(getAdapter(adapter, log));
}
