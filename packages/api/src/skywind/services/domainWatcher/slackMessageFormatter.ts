export interface SlackMessage {
    text: string;
    channel?: string;
    username?: string;
    icon_emoji?: string;
}

export interface BlockedDomainAlert {
    domain: string;
    blockedAt: Date;
    poolId?: number;
    reason?: string;
}

export interface EmptyPoolAlert {
    poolId: number;
    poolName: string;
    poolType: "static" | "dynamic";
}

export interface DefaultDomainBlockedAlert {
    domain: string;
    blockedAt: Date;
    domainType: "static" | "dynamic" | "lobby" | "liveStreaming" | "ehub";
}