export interface SlackMessage {
    text: string;
    channel?: string;
    username?: string;
    icon_emoji?: string;
}

export interface BlockedDomainAlert {
    domain: string;
    blockedAt: Date;
    poolId?: number;
    reason?: string;
}

export interface EmptyPoolAlert {
    poolId: number;
    poolName: string;
    poolType: "static" | "dynamic";
}

export interface DefaultDomainBlockedAlert {
    domain: string;
    blockedAt: Date;
    domainType: "static" | "dynamic" | "lobby" | "liveStreaming" | "ehub";
}

export class SlackMessageFormatter {
    public formatBlockedDomainMessage(alert: BlockedDomainAlert): SlackMessage {
        const timestamp = alert.blockedAt.toISOString();
        let text = "🚫 *Domain Blocked Alert*\n";
        text += `*Domain:* ${alert.domain}\n`;
        text += `*Blocked At:* ${timestamp}\n`;

        if (alert.poolId) {
            text += `*Pool ID:* ${alert.poolId}\n`;
        }

        if (alert.reason) {
            text += `*Reason:* ${alert.reason}\n`;
        }

        return {
            text,
            channel: "#blocked_domains",
            username: "Domain Monitor",
            icon_emoji: ":warning:"
        };
    }

    public formatEmptyPoolMessage(alert: EmptyPoolAlert): SlackMessage {
        let text = "⚠️ *Empty Domain Pool Alert*\n";
        text += `*Pool:* ${alert.poolName} (ID: ${alert.poolId})\n`;
        text += `*Pool Type:* ${alert.poolType}\n`;
        text += `*Status:* No active, unblocked domains available\n`;

        return {
            text,
            channel: "#blocked_domains",
            username: "Domain Monitor",
            icon_emoji: ":exclamation:"
        };
    }

    public formatDefaultDomainBlockedMessage(alert: DefaultDomainBlockedAlert): SlackMessage {
        const timestamp = alert.blockedAt.toISOString();
        let text = "🔴 *Default Domain Blocked Alert*\n";
        text += `*Domain:* ${alert.domain}\n`;
        text += `*Domain Type:* ${alert.domainType}\n`;
        text += `*Blocked At:* ${timestamp}\n`;
        text += "*Impact:* Default domain is no longer accessible\n";

        return {
            text,
            channel: "#blocked_domains",
            username: "Domain Monitor",
            icon_emoji: ":rotating_light:"
        };
    }
}
