import { Models } from "../../models/models";
import { Op } from "sequelize";
import { EntityStatus } from "../../entities/entity";
import { getSlackNotificationService } from "../slackNotification";
import { BlockedDomainAlert, DefaultDomainBlockedAlert, SlackMessageFormatter } from "./slackMessageFormatter";

export class NotificationService {
    private readonly slackService = getSlackNotificationService();
    private readonly messageFormatter = new SlackMessageFormatter();

    public async sendStaticPoolDomainBlockedAlert(
        domain: string,
        blockedAt: Date,
        poolId: number
    ): Promise<void> {
        try {
            const staticPool = await Models.StaticDomainPoolModel.findByPk(poolId);
            if (staticPool) {
                const alert: BlockedDomainAlert = {
                    domain,
                    blockedAt,
                    poolId,
                    reason: "Domain blocked by monitoring adapter"
                };

                const message = this.messageFormatter.formatBlockedDomainMessage(alert);
                await this.slackService.sendFormattedMessage(message);
            }
        } catch (error) {
            console.error(`Failed to send static pool domain blocked alert: ${error.message}`);
        }
    }

    public async sendDynamicPoolDomainBlockedAlert(
        domain: string,
        blockedAt: Date,
        poolId: number
    ): Promise<void> {
        try {
            const dynamicPool = await Models.DynamicDomainPoolModel.findByPk(poolId);
            if (dynamicPool) {
                const alert: BlockedDomainAlert = {
                    domain,
                    blockedAt,
                    poolId,
                    reason: "Domain blocked by monitoring adapter"
                };

                const message = this.messageFormatter.formatBlockedDomainMessage(alert);
                await this.slackService.sendFormattedMessage(message);
            }
        } catch (error) {
            console.error(`Failed to send dynamic pool domain blocked alert: ${error.message}`);
        }
    }

    public async sendDefaultDomainBlockedAlert(domainId: number, domain: string, blockedAt: Date): Promise<void> {
        try {
            const entitiesUsingDomain = await Models.EntityModel.findAll({
                where: {
                    [Op.or]: [
                        { staticDomainId: domainId },
                        { dynamicDomainId: domainId },
                        { lobbyDomainId: domainId },
                        { liveStreamingDomainId: domainId },
                        { ehubDomainId: domainId }
                    ],
                    status: EntityStatus.NORMAL
                }
            });

            if (entitiesUsingDomain.length > 0) {
                const entity = entitiesUsingDomain[0];
                let domainType: string;

                if (entity.staticDomainId === domainId) {
                    domainType = "static";
                } else if (entity.dynamicDomainId === domainId) {
                    domainType = "dynamic";
                } else if (entity.lobbyDomainId === domainId) {
                    domainType = "lobby";
                } else if (entity.liveStreamingDomainId === domainId) {
                    domainType = "liveStreaming";
                } else if (entity.ehubDomainId === domainId) {
                    domainType = "ehub";
                } else {
                    return;
                }

                const alert: DefaultDomainBlockedAlert = {
                    domain,
                    blockedAt,
                    domainType: domainType as any
                };

                const message = this.messageFormatter.formatDefaultDomainBlockedMessage(alert);
                await this.slackService.sendFormattedMessage(message);
            }
        } catch (error) {
            console.error(`Failed to send default domain blocked alert: ${error.message}`);
        }
    }
}
