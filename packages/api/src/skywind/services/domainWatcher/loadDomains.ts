import { Op } from "sequelize";
import { Models } from "../../models/models";
import { EntityStatus } from "../../entities/entity";
import { DomainStatus, StaticDomainType } from "../../entities/domain";
import { AdapterDomains, DomainSource, DomainSources } from "./types";

interface DomainData {
    id: number;
    domain: string;
    type?: StaticDomainType;
    environment?: string;
}

interface PoolItemData {
    isActive?: boolean;
    blockedDate?: Date;
}

interface StaticDomainPoolData {
    id: number;
    domainWatcherAdapterId: string;
    domains?: Array<DomainData & {
        StaticDomainPoolItem?: PoolItemData;
    }>;
}

interface DynamicDomainPoolData {
    id: number;
    domainWatcherAdapterId?: string;
    domains?: Array<DomainData & {
        DynamicDomainPoolItem?: PoolItemData;
    }>;
}

interface EntityWithDomains {
    id: number;
    StaticDomainPoolModel?: StaticDomainPoolData;
    DynamicDomainPoolModel?: DynamicDomainPoolData;
    staticDomain?: DomainData;
    lobbyDomain?: DomainData;
    liveStreamingDomain?: DomainData;
    ehubDomain?: DomainData;
    DynamicDomainModel?: DomainData;
}

type AdapterDomainInfo = { domainId: number; domainWatcherAdapterId: string; sources: DomainSource[]; };

export async function loadDomains(): Promise<AdapterDomains> {
    const entities = await Models.EntityModel.findAll({
        where: {
            [Op.or]: [
                {
                    staticDomainPoolId: {
                        [Op.ne]: null
                    }
                },
                {
                    dynamicDomainPoolId: {
                        [Op.ne]: null
                    }
                }
            ],
            status: EntityStatus.NORMAL
        },
        include: [
            {
                model: Models.StaticDomainPoolModel,
                where: {
                    domainWatcherAdapterId: {
                        [Op.and]: [
                            { [Op.ne]: null },
                            { [Op.ne]: "" }
                        ]
                    }
                },
                attributes: ["id", "domainWatcherAdapterId"],
                required: false,
                include: [
                    {
                        model: Models.StaticDomainModel,
                        as: "domains",
                        where: {
                            status: DomainStatus.ACTIVE
                        },
                        through: {
                            attributes: ["isActive", "blockedDate"],
                            where: {
                                isActive: true,
                                blockedDate: null
                            }
                        },
                        attributes: ["id", "domain", "type"],
                        required: false
                    }
                ]
            },
            {
                model: Models.DynamicDomainPoolModel,
                where: {
                    domainWatcherAdapterId: {
                        [Op.and]: [
                            { [Op.ne]: null },
                            { [Op.ne]: "" }
                        ]
                    }
                },
                attributes: ["id", "domainWatcherAdapterId"],
                required: false,
                include: [
                    {
                        model: Models.DynamicDomainModel,
                        as: "domains",
                        where: {
                            status: DomainStatus.ACTIVE
                        },
                        through: {
                            attributes: ["isActive", "blockedDate"],
                            where: {
                                isActive: true,
                                blockedDate: null
                            }
                        },
                        attributes: ["id", "domain", "environment"],
                        required: false
                    }
                ]
            },
            {
                model: Models.DynamicDomainModel,
                where: {
                    status: DomainStatus.ACTIVE
                },
                attributes: ["id", "domain", "environment"],
                required: false
            },
            {
                association: "staticDomain",
                where: {
                    status: DomainStatus.ACTIVE
                },
                attributes: ["id", "domain", "type"],
                required: false
            },
            {
                association: "lobbyDomain",
                where: {
                    status: DomainStatus.ACTIVE
                },
                attributes: ["id", "domain", "type"],
                required: false
            },
            {
                association: "liveStreamingDomain",
                where: {
                    status: DomainStatus.ACTIVE
                },
                attributes: ["id", "domain", "type"],
                required: false
            },
            {
                association: "ehubDomain",
                where: {
                    status: DomainStatus.ACTIVE
                },
                attributes: ["id", "domain", "type"],
                required: false
            }
        ],
        attributes: ["id", "dynamicDomainPoolId"]
    });

    const items = new Map<string, AdapterDomainInfo>();
    for (const entity of entities) {
        toDomains(entity.toJSON() as EntityWithDomains, items);
    }
    const adapterDomains: AdapterDomains = new Map();
    for (const [domain, { domainWatcherAdapterId, domainId, sources }] of items.entries()) {
        const adapterDomain: DomainSources = adapterDomains.get(domainWatcherAdapterId) || new Map();
        adapterDomain.set(domain, {
            domainId,
            sources: [
                ...(adapterDomain.get(domain)?.sources || []),
                ...sources
            ]
        });
        adapterDomains.set(domainWatcherAdapterId, adapterDomain);
    }
    return adapterDomains;
}

function toDomains(entity: EntityWithDomains, items: Map<string, AdapterDomainInfo>): void {
    const fnDomain = (domainWatcherAdapterId: string, type: DomainSource["type"]) => ({ id, domain, type: staticType, environment }: DomainData, poolId?: DomainSource["poolId"]) => {
        const existing = items.get(domain);
        items.set(domain, {
            domainId: id,
            domainWatcherAdapterId,
            sources: [{ type, staticType, domainId: id, poolId, environment }, ...existing?.sources ?? []]
        });
    };

    // Handle static domain pool
    if (entity.StaticDomainPoolModel?.domainWatcherAdapterId) {
        const { id, domainWatcherAdapterId, domains } = entity.StaticDomainPoolModel;
        const addPoolDomain = fnDomain(domainWatcherAdapterId, "staticPool" as const);
        const addEntityDomain = fnDomain(domainWatcherAdapterId, "entity" as const);

        if (Array.isArray(domains) && domains.length > 0) {
            for (const domain of domains) {
                addPoolDomain(domain, id);
            }
        }
        if (entity.staticDomain) {
            addEntityDomain(entity.staticDomain);
        }
        if (entity.lobbyDomain) {
            addEntityDomain(entity.lobbyDomain);
        }
        if (entity.liveStreamingDomain) {
            addEntityDomain(entity.liveStreamingDomain);
        }
        if (entity.ehubDomain) {
            addEntityDomain(entity.ehubDomain);
        }
    }

    // Handle dynamic domain pool
    if (entity.DynamicDomainPoolModel?.domainWatcherAdapterId) {
        const { id, domainWatcherAdapterId, domains } = entity.DynamicDomainPoolModel;
        const addPoolDomain = fnDomain(domainWatcherAdapterId, "dynamicPool" as const);
        const addEntityDomain = fnDomain(domainWatcherAdapterId, "entity" as const);

        if (Array.isArray(domains) && domains.length > 0) {
            for (const domain of domains) {
                addPoolDomain(domain, id);
            }
        }
        if (entity.DynamicDomainModel) {
            addEntityDomain(entity.DynamicDomainModel);
        }
    }
}
