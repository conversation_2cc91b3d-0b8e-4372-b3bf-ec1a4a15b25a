# Domain Pool Count Filters

This document describes the new optional filter functionality added to the `countDomains` method in both Static and Dynamic Domain Pool services.

## Overview

The `countDomains` method now supports optional filters to count domains based on specific criteria:

- **Static Domain Pools**: Filter by domain type (static, lobby, live-streaming, ehub)
- **Dynamic Domain Pools**: Filter by environment (production, test, staging, etc.)

## Usage Examples

### Static Domain Pool Service

```typescript
import { getStaticDomainPoolService } from "../services/staticDomainPool";
import { StaticDomainType } from "../entities/domain";

const service = getStaticDomainPoolService();

// Count all active domains in pool
const totalCount = await service.countDomains(poolId);

// Count only static type domains
const staticCount = await service.countDomains(poolId, { 
    type: StaticDomainType.STATIC 
});

// Count only lobby domains
const lobbyCount = await service.countDomains(poolId, { 
    type: StaticDomainType.LOBBY 
});

// Count only live streaming domains
const liveStreamingCount = await service.countDomains(poolId, { 
    type: StaticDomainType.LIVE_STREAMING 
});

// Count only ehub domains
const ehubCount = await service.countDomains(poolId, { 
    type: StaticDomainType.EHUB 
});
```

### Dynamic Domain Pool Service

```typescript
import { getDynamicDomainPoolService } from "../services/dynamicDomainPool";

const service = getDynamicDomainPoolService();

// Count all active domains in pool
const totalCount = await service.countDomains(poolId);

// Count only production environment domains
const productionCount = await service.countDomains(poolId, { 
    environment: "production" 
});

// Count only test environment domains
const testCount = await service.countDomains(poolId, { 
    environment: "test" 
});

// Count only staging environment domains
const stagingCount = await service.countDomains(poolId, { 
    environment: "staging" 
});
```

## Filter Interface Definitions

### StaticDomainCountFilter

```typescript
export interface StaticDomainCountFilter {
    type?: StaticDomainType;
}
```

### DynamicDomainCountFilter

```typescript
export interface DynamicDomainCountFilter {
    environment?: string;
}
```

## Available Static Domain Types

- `StaticDomainType.STATIC` - "static"
- `StaticDomainType.LOBBY` - "lobby"
- `StaticDomainType.LIVE_STREAMING` - "live-streaming"
- `StaticDomainType.EHUB` - "ehub"

## Notes

- All filter parameters are optional
- When no filter is provided, the method counts all active, non-blocked domains
- The existing behavior is preserved for backward compatibility
- Filters only apply to active domains with `status: "active"`, `isActive: true`, and `blockedDate: null`
